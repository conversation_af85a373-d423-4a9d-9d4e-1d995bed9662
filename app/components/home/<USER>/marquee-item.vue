<script setup lang="ts">
import { computed } from 'vue';
import { ExternalLink } from 'lucide-vue-next';
import { sanitizeHtml } from '~/composables/useMarquee';
import type { UpdateData } from '~/types/home';

interface Props {
  item: UpdateData;
  isDuplicate?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isDuplicate: false,
});

// Computed properties for accessibility and safety
const sanitizedTitle = computed(() => sanitizeHtml(props.item.title));

const linkAriaLabel = computed(() => {
  const action = props.item.buttonTitle || 'View';
  const plainTitle = props.item.title.replace(/<[^>]*>/g, '');
  return `${action} update: ${plainTitle}`;
});

const itemId = computed(() =>
  props.isDuplicate
    ? `duplicate-item-${props.item.id}`
    : `item-${props.item.id}`
);

// Handle keyboard navigation
const handleLinkKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    (event.target as HTMLElement).click();
  }
};
</script>

<template>
  <div :id="itemId" class="marquee-item" role="listitem">
    <div class="marquee-item-content">
      <!-- Title with safe HTML rendering -->
      <div class="marquee-title" v-html="sanitizedTitle" />

      <!-- Link button if available -->
      <NuxtLink
        v-if="item.link && !isDuplicate"
        :to="item.link"
        class="marquee-link"
        :aria-label="linkAriaLabel"
        target="_blank"
        rel="noopener noreferrer"
        @keydown="handleLinkKeydown"
        @click="() => console.log('Link clicked:', item.link)"
      >
        <span class="marquee-link-text">
          {{ item.buttonTitle || 'View' }}
        </span>
        <ExternalLink class="marquee-link-icon" :size="16" aria-hidden="true" />
      </NuxtLink>

      <!-- Non-interactive link for duplicates -->
      <span
        v-else-if="item.link && isDuplicate"
        class="marquee-link marquee-link-duplicate"
        aria-hidden="true"
      >
        <span class="marquee-link-text">
          {{ item.buttonTitle || 'View' }}
        </span>
        <ExternalLink class="marquee-link-icon" :size="16" aria-hidden="true" />
      </span>

      <!-- Separator -->
      <span class="marquee-separator" aria-hidden="true">•</span>
    </div>
  </div>
</template>

<style scoped>
.marquee-item {
  @apply inline-flex items-center flex-shrink-0;
}

.marquee-item-content {
  @apply font-semibold inline-flex items-center;
  @apply transition-colors duration-300;
  /* Mobile-first typography and spacing */
  font-size: 13px;
  line-height: 1.4;
  padding: 0 12px;
  /* Ensure proper vertical alignment */
  height: 100%;
  align-items: center;
  white-space: nowrap;
}

/* Responsive typography adjustments */
@media (min-width: 640px) {
  .marquee-item-content {
    font-size: 14px;
    padding: 0 16px;
  }
}

@media (min-width: 768px) {
  .marquee-item-content {
    font-size: 14px;
    padding: 0 16px;
  }
}

@media (min-width: 1024px) {
  .marquee-item-content {
    font-size: 15px;
    padding: 0 20px;
  }
}

.marquee-item-content:hover {
  @apply text-primary;
}

.marquee-title {
  @apply inline-block;
}

.marquee-link {
  @apply text-primary inline-flex items-center;
  @apply hover:underline transition-all duration-300;
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  @apply rounded-sm;
  /* Ensure links are visible and clickable */
  position: relative;
  z-index: 10;
  pointer-events: auto;
  /* Mobile-optimized spacing */
  margin-left: 6px;
  gap: 4px;
  /* Debug: Make links more visible */
  background-color: rgba(59, 130, 246, 0.1);
  padding: 2px 4px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  /* Ensure proper alignment */
  align-items: center;
  white-space: nowrap;
}

/* Responsive link spacing */
@media (min-width: 640px) {
  .marquee-link {
    margin-left: 8px;
    gap: 6px;
  }
}

@media (min-width: 768px) {
  .marquee-link {
    margin-left: 8px;
    gap: 6px;
  }
}

@media (min-width: 1024px) {
  .marquee-link {
    margin-left: 10px;
    gap: 8px;
  }
}

.marquee-link:hover {
  @apply gap-x-2;
}

.marquee-link-duplicate {
  @apply cursor-default;
  @apply hover:no-underline;
}

.marquee-link-text {
  @apply font-medium;
}

.marquee-link-icon {
  @apply transition-transform duration-300;
}

.marquee-link:hover .marquee-link-icon {
  @apply translate-x-1;
}

.marquee-separator {
  @apply text-primary/70;
  /* Mobile-first spacing */
  margin: 0 12px;
}

/* Responsive separator spacing */
@media (min-width: 640px) {
  .marquee-separator {
    margin: 0 16px;
  }
}

@media (min-width: 768px) {
  .marquee-separator {
    margin: 0 16px;
  }
}

@media (min-width: 1024px) {
  .marquee-separator {
    margin: 0 20px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .marquee-link {
    @apply underline;
  }

  .marquee-separator {
    @apply text-current;
  }
}

/* Extra small mobile screens (320px and below) */
@media (max-width: 320px) {
  .marquee-item-content {
    font-size: 12px;
    padding: 0 8px;
  }

  .marquee-link {
    margin-left: 4px;
    gap: 2px;
    padding: 1px 2px;
  }

  .marquee-separator {
    margin: 0 8px;
  }
}

/* Small mobile screens (321px - 375px) */
@media (min-width: 321px) and (max-width: 375px) {
  .marquee-item-content {
    font-size: 12px;
    padding: 0 10px;
  }

  .marquee-link {
    margin-left: 5px;
    gap: 3px;
  }

  .marquee-separator {
    margin: 0 10px;
  }
}

/* Medium mobile screens (376px - 414px) */
@media (min-width: 376px) and (max-width: 414px) {
  .marquee-item-content {
    font-size: 13px;
    padding: 0 12px;
  }

  .marquee-link {
    margin-left: 6px;
    gap: 4px;
  }

  .marquee-separator {
    margin: 0 12px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .marquee-item-content,
  .marquee-link,
  .marquee-link-icon {
    @apply transition-none;
  }
}

/* Focus management for better accessibility */
.marquee-link:focus-visible {
  @apply outline-2 outline-primary outline-offset-2;
}
</style>
