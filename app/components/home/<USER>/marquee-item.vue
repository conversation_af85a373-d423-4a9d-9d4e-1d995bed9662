<script setup lang="ts">
import { computed } from 'vue';
import { ExternalLink } from 'lucide-vue-next';
import { sanitizeHtml } from '~/composables/useMarquee';
import type { UpdateData } from '~/types/home';

interface Props {
  item: UpdateData;
  isDuplicate?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isDuplicate: false,
});

// Computed properties for accessibility and safety
const sanitizedTitle = computed(() => sanitizeHtml(props.item.title));

const linkAriaLabel = computed(() => {
  const action = props.item.buttonTitle || 'View';
  const plainTitle = props.item.title.replace(/<[^>]*>/g, '');
  return `${action} update: ${plainTitle}`;
});

const itemId = computed(() =>
  props.isDuplicate
    ? `duplicate-item-${props.item.id}`
    : `item-${props.item.id}`
);

// Handle keyboard navigation
const handleLinkKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    (event.target as HTMLElement).click();
  }
};
</script>

<template>
  <div :id="itemId" class="marquee-item" role="listitem">
    <div class="marquee-item-content">
      <!-- Title with safe HTML rendering -->
      <div class="marquee-title" v-html="sanitizedTitle" />

      <!-- Link button if available -->
      <NuxtLink
        v-if="item.link && !isDuplicate"
        :to="item.link"
        class="marquee-link"
        :aria-label="linkAriaLabel"
        target="_blank"
        rel="noopener noreferrer"
        @keydown="handleLinkKeydown"
        @click="() => console.log('Link clicked:', item.link)"
      >
        <span class="marquee-link-text">
          {{ item.buttonTitle || 'View' }}
        </span>
        <ExternalLink class="marquee-link-icon" :size="16" aria-hidden="true" />
      </NuxtLink>

      <!-- Non-interactive link for duplicates -->
      <span
        v-else-if="item.link && isDuplicate"
        class="marquee-link marquee-link-duplicate"
        aria-hidden="true"
      >
        <span class="marquee-link-text">
          {{ item.buttonTitle || 'View' }}
        </span>
        <ExternalLink class="marquee-link-icon" :size="16" aria-hidden="true" />
      </span>

      <!-- Separator -->
      <span class="marquee-separator" aria-hidden="true">•</span>
    </div>
  </div>
</template>

<style scoped>
.marquee-item {
  @apply inline-flex items-center flex-shrink-0;
}

.marquee-item-content {
  @apply text-sm font-semibold px-4 py-4 lg:py-0 inline-flex items-center;
  @apply transition-colors duration-300;
}

.marquee-item-content:hover {
  @apply text-primary;
}

.marquee-title {
  @apply inline-block;
}

.marquee-link {
  @apply text-primary ml-2 inline-flex items-center gap-x-1;
  @apply hover:underline transition-all duration-300;
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  @apply rounded-sm;
  /* Ensure links are visible and clickable */
  position: relative;
  z-index: 10;
  pointer-events: auto;
  /* Debug: Make links more visible */
  background-color: rgba(59, 130, 246, 0.1);
  padding: 2px 4px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.marquee-link:hover {
  @apply gap-x-2;
}

.marquee-link-duplicate {
  @apply cursor-default;
  @apply hover:no-underline;
}

.marquee-link-text {
  @apply font-medium;
}

.marquee-link-icon {
  @apply transition-transform duration-300;
}

.marquee-link:hover .marquee-link-icon {
  @apply translate-x-1;
}

.marquee-separator {
  @apply mx-4 text-primary/70;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .marquee-link {
    @apply underline;
  }

  .marquee-separator {
    @apply text-current;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .marquee-item-content,
  .marquee-link,
  .marquee-link-icon {
    @apply transition-none;
  }
}

/* Focus management for better accessibility */
.marquee-link:focus-visible {
  @apply outline-2 outline-primary outline-offset-2;
}
</style>
