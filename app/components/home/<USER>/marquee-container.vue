<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import MarqueeItem from './marquee-item.vue';
import type { UpdateData } from '~/types/home';

interface Props {
  items: UpdateData[];
  speed?: number;
  pauseOnHover?: boolean;
  class?: string;
  reducedMotionSpeed?: number;
}

const props = withDefaults(defineProps<Props>(), {
  speed: 30,
  pauseOnHover: true,
  class: '',
  reducedMotionSpeed: 120, // Very slow speed for reduced motion users
});

const marqueeRef = ref<HTMLElement>();
const isAnimationPaused = ref(false);
const isVisible = ref(false);

// Computed properties for accessibility and animation
const ariaLabel = computed(
  () => `Scrolling updates: ${props.items.length} items available`
);

// Always use left direction (right to left movement)
const animationDirection = 'marquee-left';

const animationDuration = computed(() => `${props.speed}s`);

const reducedMotionDuration = computed(() => `${props.reducedMotionSpeed}s`);

// Intersection Observer for performance optimization
let observer: IntersectionObserver | null = null;

const handleMouseEnter = () => {
  if (props.pauseOnHover) {
    isAnimationPaused.value = true;
  }
};

const handleMouseLeave = () => {
  if (props.pauseOnHover) {
    isAnimationPaused.value = false;
  }
};

const handleKeyDown = (event: KeyboardEvent) => {
  // Pause/resume animation with spacebar
  if (event.code === 'Space') {
    event.preventDefault();
    isAnimationPaused.value = !isAnimationPaused.value;
  }
};

const handleFocus = () => {
  // Pause animation when any item receives focus
  isAnimationPaused.value = true;
};

const handleBlur = () => {
  // Resume animation when focus leaves the marquee
  if (!marqueeRef.value?.contains(document.activeElement)) {
    isAnimationPaused.value = false;
  }
};

onMounted(() => {
  // Set up intersection observer for performance
  if (marqueeRef.value) {
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          isVisible.value = entry.isIntersecting;
        });
      },
      { threshold: 0.1 }
    );
    observer.observe(marqueeRef.value);
  }
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
});
</script>

<template>
  <div
    ref="marqueeRef"
    :class="props.class"
    :aria-label="ariaLabel"
    role="region"
    aria-live="polite"
    tabindex="0"
    :style="{
      '--reduced-motion-duration': reducedMotionDuration,
    }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @keydown="handleKeyDown"
    @focusin="handleFocus"
    @focusout="handleBlur"
  >
    <div
      class="marquee-container"
      :class="{ 'animation-paused': isAnimationPaused || !isVisible }"
    >
      <!-- Primary track -->
      <div
        class="marquee-track"
        :style="{
          animationName: animationDirection,
          animationDuration: animationDuration,
        }"
      >
        <MarqueeItem
          v-for="item in props.items"
          :key="`primary-${item.id}`"
          :item="item"
        />
      </div>

      <!-- Duplicate track for seamless loop -->
      <div
        class="marquee-track marquee-track-duplicate"
        :style="{
          animationName: animationDirection,
          animationDuration: animationDuration,
        }"
        aria-hidden="true"
      >
        <MarqueeItem
          v-for="item in props.items"
          :key="`duplicate-${item.id}`"
          :item="item"
          :is-duplicate="true"
        />
      </div>
    </div>

    <!-- Screen reader announcement for updates -->
    <div class="sr-only" aria-live="polite">
      {{ props.items.length }} updates available. Press space to pause
      scrolling.
    </div>
  </div>
</template>

<style scoped>
.marquee-container {
  @apply flex flex-nowrap justify-start relative w-full;
  /* Ensure no horizontal scrolling ever occurs */
  overflow: hidden;
  overflow-x: hidden;
  /* Smooth gradient fade effect */
  mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
}

.marquee-track {
  @apply whitespace-nowrap inline-flex items-center flex-shrink-0;
  /* Ensure seamless animation */
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-play-state: running;
  animation-fill-mode: none;
  /* Prevent any gaps or stuttering */
  will-change: transform;
}

.marquee-track-duplicate {
  /* Ensure the duplicate track is positioned correctly for seamless loop */
  margin-left: 0;
}

.animation-paused .marquee-track {
  animation-play-state: paused;
}

@keyframes marquee-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Focus styles for accessibility */
.marquee-container:focus-within {
  outline: 2px solid theme('colors.primary.DEFAULT');
  outline-offset: 2px;
}

/* Reduced motion support - use very slow animation instead of scrolling */
@media (prefers-reduced-motion: reduce) {
  .marquee-track {
    /* Use very slow animation instead of disabling completely */
    animation-name: marquee-left;
    animation-duration: var(--reduced-motion-duration, 120s) !important;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }

  .marquee-container {
    /* Ensure no scrolling even with reduced motion */
    overflow: hidden;
    overflow-x: hidden;
  }

  /* Optional: Reduce the fade effect for reduced motion users */
  .marquee-container {
    mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 5%,
      black 95%,
      transparent 100%
    );
    -webkit-mask-image: linear-gradient(
      to right,
      transparent 0%,
      black 5%,
      black 95%,
      transparent 100%
    );
  }
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
