import { ref, computed, readonly, onMounted, onUnmounted, type Ref } from 'vue';

export interface MarqueeOptions {
  pauseOnHover?: boolean;
  pauseOnFocus?: boolean;
  enableKeyboardControls?: boolean;
  visibilityThreshold?: number;
}

export function useMarquee(
  elementRef: Ref<HTMLElement | undefined>,
  options: MarqueeOptions = {}
) {
  const {
    pauseOnHover = true,
    pauseOnFocus = true,
    enableKeyboardControls = true,
    visibilityThreshold = 0.1,
  } = options;

  const isAnimationPaused = ref(false);
  const isVisible = ref(false);
  const isHovered = ref(false);
  const isFocused = ref(false);

  let observer: IntersectionObserver | null = null;

  // Computed animation state
  const shouldPauseAnimation = computed(() => {
    return (
      !isVisible.value ||
      (pauseOnHover && isHovered.value) ||
      (pauseOnFocus && isFocused.value) ||
      isAnimationPaused.value
    );
  });

  // Event handlers
  const handleMouseEnter = () => {
    if (pauseOnHover) {
      isHovered.value = true;
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover) {
      isHovered.value = false;
    }
  };

  const handleFocusIn = () => {
    if (pauseOnFocus) {
      isFocused.value = true;
    }
  };

  const handleFocusOut = (event: FocusEvent) => {
    if (pauseOnFocus) {
      // Check if focus is moving outside the marquee container
      const relatedTarget = event.relatedTarget as HTMLElement;
      if (!elementRef.value?.contains(relatedTarget)) {
        isFocused.value = false;
      }
    }
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!enableKeyboardControls) return;

    switch (event.code) {
      case 'Space':
        event.preventDefault();
        isAnimationPaused.value = !isAnimationPaused.value;
        break;
      case 'ArrowLeft':
      case 'ArrowRight':
        // Allow arrow keys to navigate through focusable elements
        // without interfering with the animation
        break;
    }
  };

  // Setup intersection observer for performance
  const setupIntersectionObserver = () => {
    if (!elementRef.value) return;

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          isVisible.value = entry.isIntersecting;
        });
      },
      { threshold: visibilityThreshold }
    );

    observer.observe(elementRef.value);
  };

  // Cleanup function
  const cleanup = () => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  };

  // Public API
  const pause = () => {
    isAnimationPaused.value = true;
  };

  const resume = () => {
    isAnimationPaused.value = false;
  };

  const toggle = () => {
    isAnimationPaused.value = !isAnimationPaused.value;
  };

  // Lifecycle
  onMounted(() => {
    setupIntersectionObserver();
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    // State
    isAnimationPaused: readonly(isAnimationPaused),
    isVisible: readonly(isVisible),
    isHovered: readonly(isHovered),
    isFocused: readonly(isFocused),
    shouldPauseAnimation: readonly(shouldPauseAnimation),

    // Event handlers
    handleMouseEnter,
    handleMouseLeave,
    handleFocusIn,
    handleFocusOut,
    handleKeyDown,

    // Control methods
    pause,
    resume,
    toggle,

    // Cleanup
    cleanup,
  };
}

// Utility function for sanitizing HTML content
export function sanitizeHtml(html: string): string {
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/data:/gi, '');
}

// Utility function for generating accessible labels
export function generateMarqueeAriaLabel(
  itemCount: number,
  itemType: string = 'items'
): string {
  return `Scrolling ${itemType}: ${itemCount} ${itemType} available. Press space to pause scrolling.`;
}

// Utility function to ensure smooth marquee animation
export function ensureMarqueeSmoothing(element: HTMLElement): void {
  // Add CSS properties to ensure smooth animation
  element.style.willChange = 'transform';
  element.style.backfaceVisibility = 'hidden';
  element.style.perspective = '1000px';
}
